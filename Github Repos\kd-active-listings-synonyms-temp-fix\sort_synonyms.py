#!/usr/bin/env python3
"""
Script to sort machine tool synonyms alphabetically while preserving comments at the top.
"""

def sort_synonyms_file(input_file, output_file=None):
    """
    Sort synonym lines alphabetically while preserving comments at the top.
    
    Args:
        input_file (str): Path to the input file
        output_file (str): Path to the output file (if None, overwrites input file)
    """
    if output_file is None:
        output_file = input_file
    
    with open(input_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Find where comments end and content begins
    comment_lines = []
    content_lines = []
    
    for line in lines:
        stripped = line.strip()
        # Consider lines starting with # or empty lines as comments/header
        if stripped.startswith('#') or stripped == '':
            comment_lines.append(line)
        else:
            # Once we hit non-comment content, everything else is content
            content_lines.extend(lines[len(comment_lines):])
            break
    
    # Remove any trailing empty lines from comments
    while comment_lines and comment_lines[-1].strip() == '':
        comment_lines.pop()
    
    # Filter out empty lines from content and sort
    non_empty_content = [line for line in content_lines if line.strip()]
    non_empty_content.sort(key=lambda x: x.strip().lower())
    
    # Write the sorted file
    with open(output_file, 'w', encoding='utf-8') as f:
        # Write comments
        for line in comment_lines:
            f.write(line)
        
        # Add a blank line after comments if there were any
        if comment_lines:
            f.write('\n')
        
        # Write sorted content
        for line in non_empty_content:
            f.write(line)
        
        # Add final newline if the last line doesn't have one
        if non_empty_content and not non_empty_content[-1].endswith('\n'):
            f.write('\n')

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) < 2:
        print("Usage: python sort_synonyms.py <input_file> [output_file]")
        print("If output_file is not specified, the input file will be overwritten.")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    try:
        sort_synonyms_file(input_file, output_file)
        if output_file:
            print(f"Sorted synonyms written to: {output_file}")
        else:
            print(f"Sorted synonyms written to: {input_file}")
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)
